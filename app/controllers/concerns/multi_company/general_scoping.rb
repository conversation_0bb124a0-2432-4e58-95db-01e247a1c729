module MultiCompany
  module GeneralScoping
    extend ActiveSupport::Concern

    def all_expanded_privileges(user = current_user)
      @all_expanded_privileges ||= begin
        id = scoped_company_user&.contributor_id
        id.present? ? ExpandedPrivilege.where(contributor_id: id) : ExpandedPrivilege.none
      end
    end

    def expanded_privileges
      @expanded_privileges ||= all_expanded_privileges.where('expanded_privileges.name = ?', privilege_name)
    end

    def self.included(base)
      base.extend(ClassMethods)
    end

    module ClassMethods
      def set_privilege_name(m = nil)
        self.class_variable_set(:@@privilege_name, m) if m
      end
    end

    def is_only_basic_read
      return false unless current_user && current_company && !current_user&.super_admin?

      @is_only_basic_read ||= current_company_user.contributor.expanded_privileges.where(name: 'HelpTicket').pluck(:permission_type).uniq == ['basicread']
    end

    def module_permissions
      permissions[privilege_name]
    end

    def privilege_name_from_path
      @privilege_name_from_path ||= begin
        name = request.path.split("/")[1].classify if request.path
        name = 'CompanyUser' if company_modules.include?(name)
        name = 'HelpTicket' if name == 'Ticket'
        name = 'TelecomService' if name == 'TelecomProvider'
        name = 'Monitoring' if name == 'MonitoringAccess'
        return name if Privilege::PRIVILEGES.include?(name)
        nil
      end
    end

    def company_modules
      ['Dashboard', 'Company', 'Subscription', 'PaymentSource', 'Report']
    end

    def is_help_desk_module?
      @is_help_desk_module ||= begin
        path = request.referer&.split("/")
        path.include?("help_tickets") || path.include?("help_center") if path
      end
    end

    def is_related_company_management_module?
      @is_related_company_management_module ||= begin
        path = request.referer&.split("/")
        path.include?("child_company_management") if path
      end
    end

    def is_help_center_module?
      @is_help_center_module ||= begin
        path = request.referer&.split("/")
        path.include?("help_center") if path
      end
    end

    def privilege_name_from_referer
      @privilege_name_from_referer ||= begin
        parts = request.referer&.split("/")
        return nil unless parts && parts[3]
        path = parts[3]
        name = path.classify if request.referer
        name = 'CompanyUser' if name == 'Dashboard'
        name = 'CompanyUser' if name == 'Company'
        name = 'HelpTicket' if name == 'Ticket'
        name = 'TelecomService' if name == 'TelecomProvider'
        name = 'Monitoring' if name == 'MonitoringAccess'
        return name if Privilege::PRIVILEGES.include?(name)
        nil
      end
    end

=begin
    def is_resource_authorized?
      return true if current_company_user&.user&.super_admin?
      privileges = expanded_privileges
      if privilege_name == "HelpTicket" && selected_workspace_id
        privileges = privileges.where(workspace_id: selected_workspace_id)
      end
      is_authorized(privileges)
    end

    def is_authorized(privileges)
      authorized_permissions = ['write', 'readscoped', 'scope', 'read']
      if privileges.present?
        privileges.each do |privilege|
          authorized_permissions.include?(privilege.permission_type)
        end
      else
        respond_to do |format|
          format.html { render 'layouts/access_denied' }
          format.json { render json: { message: "Sorry, you're not authorized to perform this action." }, status: 401 }
        end
      end
    end
=end

    def privilege_name_from_parent
      name = self.class.superclass.to_s.split("::").map do |i|
        Privilege::PRIVILEGES.include?(i.singularize) ? i.singularize : nil
      end
      name.reduce { |result, i| result || i }
    end

    def privilege_name_from_company_module
      if params[:company_module] == 'helpdesk'
        'HelpTicket'
      elsif params[:company_module] == 'location'
        'CompanyUser'
      else
        nil
      end
    end

    def privilege_name_from_class
      if self.class.class_variable_defined?(:@@privilege_name)
        p = self.class.class_variable_get(:@@privilege_name)
        return p if p && Privilege::PRIVILEGES.include?(p)
      end
      nil
    end

    def privilege_name
      @privilege_name ||= begin
        # First, if we have set the privilege_name in the params, then we use that
        if params[:privilege_name] && Privilege::PRIVILEGES.include?(params[:privilege_name])
          params[:privilege_name]

        # Otherwise, if it was set in the calss, then we use that
        elsif privilege_name_from_class
          privilege_name_from_class

        # Thirdly, we use the parent to try that
        elsif privilege_name_from_parent
          privilege_name_from_parent

        # Fourthly, we use the request referer
        elsif privilege_name_from_referer
          privilege_name_from_referer

        # Fifthly, we use the company module
        elsif privilege_name_from_company_module && Privilege::PRIVILEGES.include?(privilege_name_from_company_module)
          privilege_name_from_company_module

        # Lastly, try the path
        elsif privilege_name_from_path
          privilege_name_from_path
        end
      end
    end

    def loose_scoping
      @loose_scoping.nil? ? false : @loose_scoping
    end

    def scoped_workspace
      @scoped_workspace ||= begin
        workspace = nil
        if params[:workspace_id]
          workspace = Workspace.find_by(id: params[:workspace_id])
        elsif respond_to?(:request) && request.headers["HTTP_X_GENUITY_WORKSPACE_ID"]
          workspace = Workspace.find_by(id: request.headers["HTTP_X_GENUITY_WORKSPACE_ID"])
        end

        # Ok, this might seem a little Draconian, but I want this call to fail and
        # fail loudly if we get here.
        raise "Missing scoped workspace" unless workspace || loose_scoping
        #raise "Unauthorized workspace access" if !current_user || expanded_privileges.where(workspace: workspace).blank?
        workspace
      end
    end

    def scoped_company
      @scoped_company ||= begin
        company = nil
        if params[:company_id]
          company = Company.find_by_cache(id: params[:company_id])
        elsif params[:sub]
          company = Company.find_by_cache(subdomain: params[:sub])
        elsif respond_to?(:request) && request.headers["HTTP_X_GENUITY_COMPANY_ID"]
          company = Company.find_by_cache(id: request.headers["HTTP_X_GENUITY_COMPANY_ID"])
        elsif (respond_to?(:request) && request.format.html?) || loose_scoping
          company = current_company
        end
        # Ok, this might seem a little Draconian, but I want this call to fail and
        # fail loudly if we get here.
        raise "Missing scoped company" unless company
        #raise "Unauthorized company access" unless !current_user || all_expanded_privileges.where(company: company).present?
        company
      end
    rescue => e
      Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production? && !request.url.include?('secure')
      raise e
    end

    def scoped_company_user
      return unless current_user.present? && scoped_company.present?
  
      @scoped_company_user ||= CompanyUser.find_by_cache(user_id: current_user.id, company_id: scoped_company.id)
    end

    # Scoped workspace id's are the workspace id's that should be used to show the data we need
    # to show (ie. help ticket list).  There can be one to many workspace id's.
    # The defaults were picked based on what was commonly used.
    def scoped_workspace_ids(permission_type = %w{ read basicread write scoped readscoped }, user: current_user)
      return scoped_company.workspaces.pluck(:id) if current_user.super_admin?

      my_privileges = all_expanded_privileges(user).where(permission_type: permission_type, name: privilege_name)
      my_workspace_ids = my_privileges.pluck(:workspace_id).uniq

      if params[:workspace_id].is_a?(Array)
        return my_workspace_ids & params[:workspace_id].map(&:to_i)
      elsif params[:workspace_id].to_i > 0 && my_workspace_ids.include?(params[:workspace_id].to_i)
        return [ params[:workspace_id].to_i ].compact
      end
      my_workspace_ids
    end

    ##
    # Selected workspaces are the derived workspace id when only one workspace id can be used
    # (ie. general settings for helpdesk).  In this case, we need to get just one workspace id
    # even though the user hasn't implicitly picked one.
    # The defaults were picked based on what was commonly used.
    def selected_workspace_id(user: current_user)
      my_privileges = all_expanded_privileges(user)
      my_privileges = my_privileges.where(name: privilege_name) if privilege_name

      my_workspace_ids = my_privileges.pluck(:workspace_id)
      if params[:workspace_id].to_i > 0 && my_workspace_ids.include?(params[:workspace_id].to_i)
        return params[:workspace_id].to_i
      end

      if params[:company_id].to_i > 0
        if my_privileges.where(company_id: params[:company_id]).present?
          Workspace.where(company: params[:company_id]).order(:created_at).first.id
        else
          nil
        end
      else
        Workspace.where(id: my_privileges.pluck(:workspace_id)).order(:created_at).where(company_id: scoped_company.id)&.first&.id
      end
    end

    def linked_contributor_ids(resource)
      allow_followers = HelpdeskSetting.joins(:default_helpdesk_setting)
                              .find_by(default_helpdesk_setting: { setting_type: 'allow_followers_see_tickets' }, company: scoped_company, workspace: scoped_workspace)
                              .enabled
      field_names = allow_followers ? ['created_by', 'assigned_to', 'followers'] : ['created_by', 'assigned_to']
      value_ints = resource.custom_form_values.joins(:custom_form_field).where(custom_form_fields: { name: field_names }).pluck(:value_int).compact
      contributors = Contributor.where(id: value_ints)
      @linked_contributor_ids ||= contributors.map { |contributor| contributor.contributor_ids_only_users }.flatten
    end

    def owned_by?(resource)
      return false unless resource
      return true if privilege_name != "HelpTicket"
      linked_contributor_ids(resource).include?(scoped_company_user.contributor_id)
    end

    def resolved_can_read_resource(resource)
      return true if current_user.super_admin?
      my_privileges = expanded_privileges
      my_privileges = my_privileges.where(workspace_id: resource.workspace.id) if privilege_name == "HelpTicket" && resource.respond_to?(:workspace)

      my_privileges.find_each do |ep|
        return true if %w{ write read readscoped }.include?(ep.permission_type) || (%w{ scoped basicread }.include?(ep.permission_type) && owned_by?(resource))
      end
      return false
    end

    def resolved_can_read
      return true if current_user.super_admin?
      my_privileges = expanded_privileges
      my_privileges = my_privileges.where(workspace_id: scoped_workspace_ids) if privilege_name == "HelpTicket"
      privs = my_privileges.pluck(:permission_type)
      return (privs & ["write", "read", "scoped", "readscoped", "basicread"]).present?
    end

    def resolved_can_write_resource(resource)
      return true if current_user.super_admin?
      my_privileges = expanded_privileges
      my_privileges = my_privileges.where(workspace_id: resource.workspace.id) if privilege_name == "HelpTicket" && resource.respond_to?(:workspace)
      my_privileges.find_each do |ep|
        return true if %w{ write }.include?(ep.permission_type) || (%w{scoped readscoped basicread}.include?(ep.permission_type) && owned_by?(resource))
      end
      return false
    end

    def resolved_can_write
      return true if current_user.super_admin?
      my_privileges = expanded_privileges
      my_privileges = my_privileges.where(workspace_id: scoped_workspace_ids) if privilege_name == "HelpTicket"
      privs = my_privileges.pluck(:permission_type)
      return (privs & ["write", "scoped", "readscoped", "basicread"]).present?
    end

    def can_read?
      @can_read ||= resolved_can_read
    end

    def can_write?
      @can_write ||= resolved_can_write
    end

    def can_read_resource?(resource)
      @can_read_resource ||= resolved_can_read_resource(resource)
    end

    def can_write_resource?(resource)
      @can_write_resource ||= resolved_can_write_resource(resource)
    end

    def authorize_read(resource = nil)
      if resource
        raise Exceptions::AccessDenied unless can_read_resource?(resource)
      else
        raise Exceptions::AccessDenied unless can_read?
      end
    end

    def authorize_write(resource = nil)
      if resource
        raise Exceptions::AccessDenied unless can_write_resource?(resource)
      else
        raise Exceptions::AccessDenied unless can_write?
      end
    end

    def has_write_company_permission?
      if current_company.present?
        return current_user.super_admin? || all_expanded_privileges.exists?(name: 'CompanyUser', permission_type: 'write')
      end
      return false
    end

    def has_permission?
      if current_company.present?
        return current_user.super_admin? || all_expanded_privileges.exists?(name: 'CompanyUser')
      end
      return false
    end

    def render_response(response)
      render json: response[:response_body], status: response[:response_status]
    end

    def verify_super_admin_mfa
      return if Rails.env.development? || current_user.email == "<EMAIL>"

      system_user = SystemUser.where(user_id: current_user.id, mfa_verified: true).where.not(mfa_secret_key: nil).first
      if system_user.present?
        last_verification_hours = nil
        mfa_session = MfaSuperAdminSession.find_by(user_id: current_user.id)
        if mfa_session.present?
          last_verification = Time.current - mfa_session.mfa_verified_at
          last_verification_hours = last_verification / 3600
        end
        return if last_verification_hours.present? && last_verification_hours < 8

        MfaService.new(params, system_user, system_user.mfa_code, system_user.company).mfa_info
        redirect_url = request.path
        redirect_to mfa_index_path(redirect_url: redirect_url)
      end
    end
  end
end
