namespace :investigate do
  desc "Investigate 403 errors for affected companies"
  task check_403_companies: :environment do
    puts "=" * 80
    puts "INVESTIGATING 403 FORBIDDEN ERRORS"
    puts "=" * 80
    puts

    # Affected company subdomains
    affected_subdomains = [
      'door-international',
      'ndsigroup', 
      'shell-fcu',
      'expresssolicitors'
    ]

    affected_subdomains.each do |subdomain|
      puts "🔍 INVESTIGATING: #{subdomain}"
      puts "-" * 50
      
      company = Company.find_by(subdomain: subdomain)
      
      if company.nil?
        puts "❌ ERROR: Company not found for subdomain '#{subdomain}'"
        puts
        next
      end

      puts "✅ Company found: #{company.name} (ID: #{company.id})"
      
      # Check company verification status
      puts "📋 Company Status:"
      puts "  - Verified: #{company.verified}"
      puts "  - Sample Company: #{company.is_sample_company?}"
      puts "  - Created: #{company.created_at}"
      puts "  - Subdomain: #{company.subdomain}"
      
      # Check subscriptions
      puts "💳 Subscription Status:"
      subscriptions = company.subscriptions.where.not(status: 'insolvent')
      if subscriptions.any?
        subscriptions.each do |sub|
          puts "  - Module: #{sub.module_type}, Status: #{sub.status}, Expires: #{sub.expires_at}"
        end
      else
        puts "  - No active subscriptions found"
      end
      
      # Check free trial status
      puts "🆓 Free Trial Status:"
      puts "  - Has current free trial: #{company.has_current_free_trial?}"
      puts "  - Trial start: #{company.trial_start_date}"
      puts "  - Trial end: #{company.trial_end_date}"
      
      # Check helpdesk access specifically
      puts "🎫 Helpdesk Access:"
      helpdesk_path = "/help_tickets/dashboard"
      puts "  - Can access helpdesk path: #{company.allow_access?(helpdesk_path)}"
      
      # Check company users with access
      puts "👥 Company Users with Access:"
      company_users = company.company_users.access_granted.includes(:user)
      if company_users.any?
        company_users.limit(5).each do |cu|
          puts "  - #{cu.user.email} (Admin: #{cu.is_admin?}, Access granted: #{cu.granted_access_at})"
        end
        puts "  - Total users with access: #{company_users.count}"
      else
        puts "  - No users with granted access found"
      end
      
      # Check workspaces
      puts "🏢 Workspaces:"
      workspaces = company.workspaces
      if workspaces.any?
        workspaces.each do |workspace|
          puts "  - #{workspace.name} (ID: #{workspace.id}, Module: #{workspace.company_module})"
        end
      else
        puts "  - No workspaces found"
      end
      
      # Check custom domains
      puts "🌐 Custom Domains:"
      custom_domains = company.custom_domains
      if custom_domains.any?
        custom_domains.each do |domain|
          puts "  - #{domain.name} (Active: #{domain.active_domain})"
        end
      else
        puts "  - No custom domains configured"
      end
      
      puts
    end
    
    puts "=" * 80
    puts "DOMAIN VALIDATION CHECK"
    puts "=" * 80
    puts
    
    # Test domain validation logic
    affected_subdomains.each do |subdomain|
      puts "🔍 Testing domain validation for: #{subdomain}.gogenuity.com"
      
      # Simulate the domain checks that happen in the application
      test_host = "#{subdomain}.gogenuity.com"
      root_domain = Rails.application.credentials.root_domain
      
      puts "  - Test host: #{test_host}"
      puts "  - Root domain: #{root_domain}"
      puts "  - Has subdomain pattern: #{test_host.end_with?(".#{root_domain}")}"
      puts "  - Extracted subdomain: #{test_host.split('.').first if test_host.end_with?(".#{root_domain}")}"
      puts "  - Is secure subdomain: #{test_host.split('.').first == 'secure' if test_host.end_with?(".#{root_domain}")}"
      puts "  - Is company subdomain: #{test_host.split('.').first != 'secure' if test_host.end_with?(".#{root_domain}")}"
      puts
    end
  end

  desc "Check recent authentication logs and errors"
  task check_auth_logs: :environment do
    puts "=" * 80
    puts "CHECKING RECENT AUTHENTICATION ISSUES"
    puts "=" * 80
    puts

    # Check for recent Bugsnag errors related to authentication
    puts "🐛 Recent Error Patterns to Look For:"
    puts "  - Check Bugsnag for 'ensure_access' related errors"
    puts "  - Look for 'AccessCheck' service errors"
    puts "  - Search for 'common?' method failures"
    puts "  - Check for 'is_secure_subdomain?' issues"
    puts

    # Check Rails logs for authentication patterns
    puts "📋 Log Analysis Commands:"
    puts "  Run these commands on your production servers:"
    puts
    puts "  # Check for ensure_access log messages"
    puts "  grep -i 'does NOT have common access' /var/log/puma/puma.log | tail -20"
    puts
    puts "  # Check for domain validation issues"
    puts "  grep -i 'is NOT a secure domain' /var/log/puma/puma.log | tail -20"
    puts
    puts "  # Check for Rack::Attack blocks"
    puts "  grep -i 'rack.attack' /var/log/puma/puma.log | tail -20"
    puts
    puts "  # Check for 403 responses"
    puts "  grep ' 403 ' /var/log/puma/puma.log | tail -20"
    puts
  end

  desc "Test authentication for specific users"
  task test_user_auth: :environment do
    puts "=" * 80
    puts "TESTING USER AUTHENTICATION"
    puts "=" * 80
    puts

    # Test emails from the affected companies
    test_emails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ]

    test_emails.each do |email|
      puts "🔍 Testing authentication for: #{email}"
      puts "-" * 50
      
      user = User.find_by(email: email)
      
      if user.nil?
        puts "❌ User not found: #{email}"
        puts
        next
      end

      puts "✅ User found: #{user.first_name} #{user.last_name} (ID: #{user.id})"
      puts "  - Email confirmed: #{user.has_confirmed_email?}"
      puts "  - Super admin: #{user.super_admin?}"
      puts "  - Created: #{user.created_at}"
      
      # Check company associations
      puts "🏢 Company Associations:"
      user.company_users.includes(:company).each do |cu|
        company = cu.company
        puts "  - Company: #{company.name} (#{company.subdomain})"
        puts "    * Access granted: #{cu.granted_access_at}"
        puts "    * Is admin: #{cu.is_admin?}"
        puts "    * Company verified: #{company.verified}"
        
        # Test AccessCheck for this user/company combination
        access_check = AccessCheck.new(user, company)
        puts "    * Common access: #{access_check.common?}"
        puts "    * Admin access: #{access_check.admin?}"
      end
      
      puts
    end
  end

  desc "Check middleware and request handling"
  task check_middleware: :environment do
    puts "=" * 80
    puts "MIDDLEWARE AND REQUEST HANDLING CHECK"
    puts "=" * 80
    puts

    puts "🔧 Current Middleware Configuration:"
    puts "  - Rack::Attack enabled: #{defined?(Rack::Attack)}"
    puts "  - HandleBadRequest middleware: #{defined?(HandleBadRequest)}"
    puts
    
    puts "🌐 Domain Configuration:"
    puts "  - Root domain: #{Rails.application.credentials.root_domain}"
    puts "  - Environment: #{Rails.env}"
    puts
    
    puts "📋 CORS Configuration:"
    if Rails.env.staging?
      puts "  - Staging CORS pattern: /\\Ahttp(s)?:\\/\\/(.+\\.)?gogenuity(\\-staging)?\\.com\\z/"
    elsif Rails.env.production?
      puts "  - Production CORS: Check application.rb for production CORS settings"
    end
    puts
    
    puts "🔒 Security Headers:"
    puts "  - Content Security Policy configured"
    puts "  - X-Content-Type-Options: nosniff"
    puts "  - Check .platform/nginx/conf.d/elasticbeanstalk/webpack.conf for deny rules"
    puts
  end
end
