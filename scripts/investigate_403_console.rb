# Quick console script to investigate 403 errors
# Run this in Rails console: load 'scripts/investigate_403_console.rb'

puts "=" * 80
puts "QUICK 403 ERROR INVESTIGATION"
puts "=" * 80

# Affected company subdomains
affected_subdomains = ['door-international', 'ndsigroup', 'shell-fcu', 'expresssolicitors']

affected_subdomains.each do |subdomain|
  puts "\n🔍 #{subdomain}:"
  
  company = Company.find_by(subdomain: subdomain)
  next puts "  ❌ Company not found" if company.nil?
  
  puts "  ✅ #{company.name}"
  puts "  📋 Verified: #{company.verified}, Sample: #{company.is_sample_company?}"
  
  # Quick subscription check
  active_subs = company.subscriptions.where.not(status: 'insolvent').count
  puts "  💳 Active subscriptions: #{active_subs}"
  
  # Free trial check
  puts "  🆓 Free trial: #{company.has_current_free_trial?}"
  
  # Helpdesk access check
  helpdesk_access = company.allow_access?("/help_tickets/dashboard")
  puts "  🎫 Helpdesk access: #{helpdesk_access}"
  
  # User count with access
  users_with_access = company.company_users.access_granted.count
  puts "  👥 Users with access: #{users_with_access}"
  
  # Check if any users are admins
  admin_count = company.company_users.access_granted.where(is_admin: true).count
  puts "  👑 Admin users: #{admin_count}"
  
  # Test AccessCheck for first user
  first_user = company.company_users.access_granted.includes(:user).first
  if first_user
    access_check = AccessCheck.new(first_user.user, company)
    puts "  🔐 Sample user access - Common: #{access_check.common?}, Admin: #{access_check.admin?}"
  end
end

puts "\n" + "=" * 80
puts "DOMAIN VALIDATION TEST"
puts "=" * 80

root_domain = Rails.application.credentials.root_domain
puts "Root domain: #{root_domain}"

affected_subdomains.each do |subdomain|
  test_host = "#{subdomain}.gogenuity.com"
  has_subdomain = test_host.end_with?(".#{root_domain}")
  extracted_subdomain = test_host.split('.').first if has_subdomain
  is_secure = extracted_subdomain == 'secure' if has_subdomain
  is_company = extracted_subdomain != 'secure' if has_subdomain
  
  puts "\n🌐 #{test_host}:"
  puts "  - Has subdomain pattern: #{has_subdomain}"
  puts "  - Extracted subdomain: #{extracted_subdomain}"
  puts "  - Is secure subdomain: #{is_secure}"
  puts "  - Is company subdomain: #{is_company}"
end

puts "\n" + "=" * 80
puts "INVESTIGATION COMPLETE"
puts "=" * 80
puts "\nTo run full investigation: rake investigate:check_403_companies"
puts "To check auth logs: rake investigate:check_auth_logs"
puts "To test specific users: rake investigate:test_user_auth"
